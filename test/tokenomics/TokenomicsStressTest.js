/**
 * Tokenomics Stress Testing Framework
 * 
 * This framework simulates real user behavior patterns to test treasury sustainability
 * and creator reward distribution on the local Hardhat blockchain (Chain ID 31337).
 * 
 * Key Features:
 * - Real user behavior simulation (not arbitrary transactions)
 * - Treasury sustainability validation
 * - Creator reward distribution testing (50% for Mystical Environments)
 * - ETH Test Mode integration with 90% discounts
 * - Multi-user coordination scenarios
 */

import { ethers } from 'ethers';
import fetch from 'node-fetch';
import { UserSimulator } from './UserSimulator.js';
import { TransactionTracker } from './TransactionTracker.js';
import { TreasuryMonitor } from './TreasuryMonitor.js';
import { ValidationSystem } from './ValidationSystem.js';
// Import GAME_CONFIG - using dynamic import since ES modules have path issues
const GAME_CONFIG = {
    POWER_UP_COSTS: {
        REALITY_WARP: 25000
    }
};

export class TokenomicsStressTest {
    constructor(config = {}) {
        // Configuration
        this.config = {
            apiBaseUrl: config.apiBaseUrl || 'http://localhost:3001/api',
            hardhatUrl: config.hardhatUrl || 'http://localhost:8545',
            chainId: config.chainId || 31337,
            hotWalletAddress: config.hotWalletAddress || '******************************************',
            testDuration: config.testDuration || 300000, // 5 minutes default
            maxConcurrentUsers: Math.min(config.maxConcurrentUsers || 5, 5), // Limit to max 5 concurrent users to prevent server overload
            ...config
        };

        // Enhanced global rate limiting and request batching - FIXED QUEUE MANAGEMENT
        this.globalRateLimiter = {
            activeRequests: 0,
            maxConcurrentRequests: 2, // REDUCED: Conservative limit to prevent server overload
            requestQueue: [],
            isProcessing: false,
            queueProcessor: null,
            maxQueueWaitTime: 90000, // 90 seconds max wait in queue (increased for stability)
            
            // NEW: Async queue acquisition that never exceeds server capacity
            async acquireGlobalSlot() {
                return new Promise((resolve, reject) => {
                    const queueItem = {
                        resolve,
                        reject,
                        timestamp: Date.now(),
                        id: Math.random().toString(36).substr(2, 9),
                        retryCount: 0,
                        maxRetries: 3
                    };

                    // Add to queue immediately
                    this.requestQueue.push(queueItem);

                    // Start processing if not already running
                    if (!this.isProcessing) {
                        this.startQueueProcessor();
                    }

                    // Set a hard timeout for the request
                    setTimeout(() => {
                        if (this.requestQueue.includes(queueItem)) {
                            this.requestQueue = this.requestQueue.filter(item => item !== queueItem);
                            reject(new Error(`Queue acquisition timed out after ${this.maxQueueWaitTime}ms`));
                        }
                    }, this.maxQueueWaitTime);
                });
            },
            
            // FIXED: Intelligent queue processor that respects server limits
            async startQueueProcessor() {
                if (this.isProcessing) return;
                this.isProcessing = true;
                
                // Use a single async function instead of setInterval
                const processQueue = async () => {
                    while (this.requestQueue.length > 0 || this.activeRequests > 0) {
                        if (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
                            await this.processQueuedRequests();
                        }
                        // Small delay to prevent CPU spinning
                        await new Promise(resolve => setTimeout(resolve, 10));
                    }
                    
                    // Only stop when both queue is empty AND no active requests
                    this.stopQueueProcessor();
                };
                
                // Start processing
                processQueue().catch(error => {
                    console.error('Queue processor error:', error);
                    this.stopQueueProcessor();
                });
            },
            
            // NEW: Stop queue processor when not needed
            stopQueueProcessor() {
                if (this.queueProcessor) {
                    clearInterval(this.queueProcessor);
                    this.queueProcessor = null;
                }
                this.isProcessing = false;
            },

            // NEW: Force restart of queue processor to ensure it keeps running
            ensureQueueProcessorRunning() {
                if (!this.isProcessing && this.requestQueue.length > 0) {
                    console.log('🔄 Restarting queue processor');
                    this.startQueueProcessor();
                }
            },
            
            // FIXED: Release slot with immediate queue processing
            releaseGlobalSlot() {
                if (this.activeRequests > 0) {
                    this.activeRequests--;
                    
                    // Use setImmediate to prevent race conditions
                    setImmediate(() => {
                        if (this.requestQueue.length > 0) {
                            this.processQueuedRequests();
                        }
                    });
                }
            },

            // FIXED: Process queued requests immediately without waiting for interval
            async processQueuedRequests() {
                const now = Date.now();
                const remainingQueue = [];
                
                // Process timeout checks sequentially (not with filter)
                for (const item of this.requestQueue) {
                    const waitTime = now - item.timestamp;
                    if (waitTime > this.maxQueueWaitTime) {
                        // Before rejecting, verify server availability
                        try {
                            const serverAvailable = await this.verifyServerAvailability();
                            if (serverAvailable && this.activeRequests < this.maxConcurrentRequests) {
                                // Server available and within capacity, give more time
                                remainingQueue.push(item);
                                continue;
                            }
                        } catch (error) {
                            // Server verification failed, proceed with timeout
                        }
                        
                        this.trackTimeout();
                        item.reject(new Error(`Request timed out after waiting ${this.maxQueueWaitTime}ms in queue`));
                    } else {
                        remainingQueue.push(item);
                    }
                }
                
                this.requestQueue = remainingQueue;
                
                // Process available requests immediately
                while (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
                    const nextRequest = this.requestQueue.shift();
                    this.activeRequests++;
                    
                    try {
                        this.trackRequestProcessed();
                        nextRequest.resolve(); // This releases the waiting promise
                    } catch (error) {
                        // If resolve fails, release the slot back
                        this.activeRequests--;
                        console.error('Error resolving queued request:', error);
                    }
                }
                
                // Stop processor if queue is empty
                if (this.requestQueue.length === 0) {
                    this.stopQueueProcessor();
                }
            },

            // NEW: Verify actual server state before rejecting requests
            async verifyServerAvailability() {
                try {
                    const response = await fetch(`${this.config.apiBaseUrl}/health`);
                    if (response.ok) {
                        const health = await response.json();
                        return health.status === 'OK';
                    }
                    return false;
                } catch (error) {
                    return false;
                }
            },
            
            // NEW: Get detailed queue status for monitoring with real-time sync
            async getQueueStatus() {
                const status = {
                    activeRequests: this.activeRequests,
                    maxConcurrentRequests: this.maxConcurrentRequests,
                    availableSlots: this.maxConcurrentRequests - this.activeRequests,
                    queueLength: this.requestQueue.length,
                    isProcessing: this.isProcessing,
                    maxWaitTime: this.maxQueueWaitTime,
                    lastUpdated: Date.now()
                };

                // Add real-time server state verification
                try {
                    status.serverAvailable = await this.verifyServerAvailability();
                } catch (error) {
                    status.serverAvailable = false;
                }

                // Calculate queue health metrics
                status.queueHealth = this.calculateQueueHealth();
                status.processedRequests = this.processedRequests || 0;
                status.totalTimeouts = this.totalTimeouts || 0;

                return status;
            },

            // NEW: Calculate queue health metrics
            calculateQueueHealth() {
                if (this.requestQueue.length === 0) return 'excellent';

                const avgWaitTime = this.requestQueue.reduce((sum, item) => {
                    return sum + (Date.now() - item.timestamp);
                }, 0) / this.requestQueue.length;

                if (avgWaitTime > this.maxQueueWaitTime * 0.8) return 'critical';
                if (avgWaitTime > this.maxQueueWaitTime * 0.5) return 'warning';
                return 'good';
            },
            
            // NEW: Cleanup method for test completion
            cleanup() {
                this.stopQueueProcessor();
                // Reject all remaining queued requests
                this.requestQueue.forEach(item => {
                    item.reject(new Error('Test completed - queued requests cancelled'));
                });
                this.requestQueue = [];
            },

            // NEW: Track request processing metrics
            trackRequestProcessed() {
                this.processedRequests = (this.processedRequests || 0) + 1;
            },

            trackTimeout() {
                this.totalTimeouts = (this.totalTimeouts || 0) + 1;
            },

            // NEW: Handle failed requests with retry logic
            async handleFailedRequest(originalRequest, error) {
                console.warn(`⚠️ Request failed: ${error.message}`);

                if (error.message.includes('429') || error.message.includes('rate limit')) {
                    // For rate limiting, release slot and retry
                    this.activeRequests--;
                    console.log('🔄 Released slot due to rate limiting, will retry');

                    // Wait a bit before retrying
                    await this.delay(1000);

                    // Retry the request
                    return this.acquireGlobalSlot();
                } else if (error.message.includes('timeout') || error.message.includes('network')) {
                    // For network issues, also retry
                    this.activeRequests--;
                    await this.delay(500);
                    return this.acquireGlobalSlot();
                } else {
                    // For other errors, just release the slot
                    this.activeRequests--;
                    throw error;
                }
            },
        };

        // Request batching system for wallet operations
        this.requestBatcher = {
            pendingRequests: new Map(),
            batchSize: 5,
            batchTimeout: 2000, // 2 seconds
            isProcessing: false
        };

        // Connection pool management
        this.connectionPool = {
            activeConnections: 0,
            maxConnections: 10,
            connectionQueue: [],
            connectionTimeout: 30000 // 30 seconds
        };

        // Setup error handling for EPIPE errors
        this.setupErrorHandling();

        // State tracking
        this.isRunning = false;
        this.startTime = null;
        this.testResults = {
            transactions: [],
            balanceHistory: [],
            errors: [],
            userBehaviors: {},
            treasuryMetrics: {},
            creatorRewards: []
        };

        // User simulators
        this.userSimulators = [];
        this.transactionTracker = null;
        this.treasuryMonitor = null;
        this.validationSystem = null;

        // Ethereum provider for direct blockchain interaction
        console.log(`🔍 DEBUG: Connecting to Hardhat at: ${this.config.hardhatUrl}`);
        console.log(`🔍 DEBUG: Hot wallet address: ${this.config.hotWalletAddress}`);
        this.provider = new ethers.JsonRpcProvider(this.config.hardhatUrl);

        console.log('🧪 TokenomicsStressTest initialized');
        console.log(`📊 Configuration:`, this.config);
    }

    /**
     * Setup error handling for EPIPE and other stream errors
     */
    setupErrorHandling() {
        // Handle stdout/stderr errors to prevent EPIPE crashes
        if (process.stdout && typeof process.stdout.on === 'function') {
            process.stdout.on('error', (error) => {
                if (error.code === 'EPIPE') {
                    // Silently ignore EPIPE errors on stdout
                    return;
                }
                // Log other stdout errors
                console.error('stdout error:', error);
            });
        }

        if (process.stderr && typeof process.stderr.on === 'function') {
            process.stderr.on('error', (error) => {
                if (error.code === 'EPIPE') {
                    // Silently ignore EPIPE errors on stderr
                    return;
                }
                // Log other stderr errors
                console.error('stderr error:', error);
            });
        }
    }

    /**
     * Initialize the stress testing framework
     */
    async initialize() {
        try {
            console.log('🚀 Initializing Tokenomics Stress Test Framework...');

            // Check server availability
            await this.checkServerHealth();

            // Initialize blockchain connection
            await this.initializeBlockchain();

            // Verify network connection
            const network = await this.provider.getNetwork();
            console.log(`🔍 DEBUG: Connected to network - Chain ID: ${network.chainId}, Name: ${network.name}`);
            console.log(`🔍 DEBUG: Expected Chain ID: ${this.config.chainId}`);

            // Initialize monitoring systems
            this.initializeMonitoring();

            // Create user simulators
            await this.createUserSimulators();

            console.log('✅ Stress test framework initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize stress test framework:', error);
            throw error;
        }
    }

    /**
     * Check if the game server is available
     */
    async checkServerHealth() {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/health`);
            if (!response.ok) {
                throw new Error(`Server health check failed: ${response.status}`);
            }
            const health = await response.json();
            console.log('✅ Server health check passed:', health);
        } catch (error) {
            throw new Error(`Server not available: ${error.message}`);
        }
    }

    /**
     * Initialize blockchain connection and verify setup
     */
    async initializeBlockchain() {
        try {
            // Test provider connection first
            const blockNumber = await this.provider.getBlockNumber();
            console.log(`🔍 DEBUG: Provider connected - Current block number: ${blockNumber}`);

            // Check network
            const network = await this.provider.getNetwork();
            console.log(`🔗 Connected to network:`, network);

            if (Number(network.chainId) !== this.config.chainId) {
                throw new Error(`Wrong chain ID. Expected ${this.config.chainId}, got ${network.chainId}`);
            }

            // Check hot wallet balance
            console.log(`🔍 DEBUG: Reading balance for address: ${this.config.hotWalletAddress}`);
            console.log(`🔍 DEBUG: Expected address: ******************************************`);
            console.log(`🔍 DEBUG: Addresses match: ${this.config.hotWalletAddress.toLowerCase() === '******************************************'}`);
            console.log(`🔍 DEBUG: Provider URL: ${this.config.hardhatUrl}`);

            // Test direct RPC call to compare with ethers.js
            console.log(`🔍 DEBUG: Making direct RPC call to ${this.config.hardhatUrl}`);
            try {
                const directRpcResponse = await fetch(this.config.hardhatUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        jsonrpc: '2.0',
                        method: 'eth_getBalance',
                        params: [this.config.hotWalletAddress, 'latest'],
                        id: 99  // Using same ID as your curl command
                    })
                });

                if (!directRpcResponse.ok) {
                    console.error(`🔍 DEBUG: RPC response not OK: ${directRpcResponse.status} ${directRpcResponse.statusText}`);
                } else {
                    const directRpcResult = await directRpcResponse.json();
                    console.log(`🔍 DEBUG: Direct RPC result: ${JSON.stringify(directRpcResult)}`);

                    if (directRpcResult.result) {
                        const directBalanceWei = BigInt(directRpcResult.result);
                        console.log(`🔍 DEBUG: Direct RPC balance in wei: ${directBalanceWei.toString()}`);
                        console.log(`🔍 DEBUG: Direct RPC balance in ETH: ${ethers.formatEther(directBalanceWei)} ETH`);
                        console.log(`🔍 DEBUG: Expected 100,000 ETH in hex: 0x21e19e0c9bab2400000`);
                        console.log(`🔍 DEBUG: Actual result hex: ${directRpcResult.result}`);
                        console.log(`🔍 DEBUG: Balances match expected: ${directRpcResult.result === '0x21e19e0c9bab2400000'}`);
                    } else {
                        console.error(`🔍 DEBUG: No result in RPC response: ${JSON.stringify(directRpcResult)}`);
                    }
                }
            } catch (error) {
                console.error('🔍 DEBUG: Direct RPC call failed:', error);
            }

            // Try multiple times to ensure we get the correct balance
            let hotWalletBalance;
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts) {
                attempts++;
                console.log(`🔍 DEBUG: Balance read attempt ${attempts}/${maxAttempts}`);

                hotWalletBalance = await this.provider.getBalance(this.config.hotWalletAddress);
                console.log(`🔍 DEBUG: Attempt ${attempts} - Raw balance in wei: ${hotWalletBalance.toString()}`);
                console.log(`🔍 DEBUG: Attempt ${attempts} - Formatted balance: ${ethers.formatEther(hotWalletBalance)} ETH`);

                // If balance is reasonable (> 1000 ETH), break
                const balanceETH = parseFloat(ethers.formatEther(hotWalletBalance));
                if (balanceETH > 1000) {
                    console.log(`🔍 DEBUG: Got reasonable balance on attempt ${attempts}: ${balanceETH} ETH`);
                    break;
                }

                if (attempts < maxAttempts) {
                    console.log(`🔍 DEBUG: Balance too low (${balanceETH} ETH), retrying in 1 second...`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            console.log(`🔍 DEBUG: Final balance in wei: ${hotWalletBalance.toString()}`);
            console.log(`🔍 DEBUG: Expected 100,000 ETH = 0x56bc75e2d63100000 wei = 100000000000000000000000 wei`);
            console.log(`🔍 DEBUG: Final formatted balance: ${ethers.formatEther(hotWalletBalance)} ETH`);
            console.log(`💰 Hot wallet balance: ${ethers.formatEther(hotWalletBalance)} ETH`);

            // Get test accounts from Hardhat
            const accounts = await this.getTestAccounts();
            console.log(`👥 Found ${accounts.length} test accounts`);

            this.testAccounts = accounts;
        } catch (error) {
            throw new Error(`Blockchain initialization failed: ${error.message}`);
        }
    }

    /**
     * Get test accounts from Hardhat node with private keys
     */
    async getTestAccounts() {
        try {
            // Hardhat default test account private keys (well-known for testing)
            const hardhatPrivateKeys = [
                '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80',
                '0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d',
                '0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a',
                '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6',
                '0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a',
                '0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba',
                '0x92db14e403b83dfe3df233f83dfa3a0d7096f21ca9b0d6d6b8d88b2b4ec1564e',
                '0x4bbbf85ce3377467afe5d46f804f221813b2bb87f24d81f60f1fcdbf7cbf4356',
                '0xdbda1821b80551c9d65939329250298aa3472ba22feea921c0cf5d620ea67b97',
                '0x2a871d0798f97d79848a013d4936a73bf4cc922c825d33c1cf7073dff6d409c6',
                '0xf214f2b2cd398c806f84e317254e0f0b801d0643303237d97a22a48e01628897',
                '0x701b615bbdfb9de65240bc28bd21bbc0d996645a3dd57e7b12bc2bdf6f192c82',
                '0xa267530f49f8280200edf313ee7af6b827f2a8bce2897751d06a843f644967b1',
                '0x47c99abed3324a2707c28affff1267e45918ec8c3f20b8aa892e8b065d2942dd',
                '0xc526ee95bf44d8fc405a158bb884d9d1238d99f0612e9f33d006bb0789009aaa',
                '0x8166f546bab6da521a8369cab06c5d2b9e46670292d85c875ee9ec20e84ffb61',
                '0xea6c44ac03bff858b476bba40716402b03e41b8e97e276d1baec7c37d42484a0',
                '0x689af8efa8c651a91ad287602527f3af2fe9f6501a7ac4b061667b5a93e037fd',
                '0xde9be858da4a475276426320d5e9262ecfc3ba460bfac56360bfa6c4c28b4ee0',
                '0xdf57089febbacf7ba0bc227dafbffa9fc08a93fdc68e1e42411a14efcf23656e'
            ];

            // Get accounts from Hardhat node
            const response = await fetch(this.config.hardhatUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'eth_accounts',
                    params: [],
                    id: 1
                })
            });

            const result = await response.json();
            if (result.error) {
                throw new Error(result.error.message);
            }

            const accounts = result.result;

            // Create wallet objects with private keys and get balances
            const accountsWithWallets = await Promise.all(
                accounts.slice(0, hardhatPrivateKeys.length).map(async (address, index) => {
                    const balance = await this.provider.getBalance(address);
                    const wallet = new ethers.Wallet(hardhatPrivateKeys[index], this.provider);

                    return {
                        address,
                        balance: ethers.formatEther(balance),
                        balanceWei: balance,
                        privateKey: hardhatPrivateKeys[index],
                        wallet: wallet
                    };
                })
            );

            return accountsWithWallets;
        } catch (error) {
            throw new Error(`Failed to get test accounts: ${error.message}`);
        }
    }

    /**
     * Initialize monitoring systems
     */
    initializeMonitoring() {
        this.transactionTracker = new TransactionTracker(this.config);
        this.treasuryMonitor = new TreasuryMonitor(this.config, this.provider);
        this.validationSystem = new ValidationSystem(this.config);
        console.log('📊 Monitoring systems initialized');
    }

    /**
     * Create user simulators for different behavior patterns
     */
    async createUserSimulators() {
        const userTypes = [
            { type: 'grinder', count: 3 },
            { type: 'whale', count: 2 },
            { type: 'creator', count: 2 },
            { type: 'casual', count: 3 }
        ];

        let accountIndex = 0;
        for (const userType of userTypes) {
            for (let i = 0; i < userType.count; i++) {
                if (accountIndex >= this.testAccounts.length) {
                    console.warn(`⚠️ Not enough test accounts for all user simulators`);
                    break;
                }

                const account = this.testAccounts[accountIndex];
                const simulator = new UserSimulator({
                    type: userType.type,
                    account,
                    config: this.config,
                    id: `${userType.type}_${i + 1}`,
                    transactionTracker: this.transactionTracker,
                    treasuryMonitor: this.treasuryMonitor,
                    globalRateLimiter: this
                });

                this.userSimulators.push(simulator);
                accountIndex++;
            }
        }

        // Initialize wallet balances for ETH test mode
        console.log('💰 Initializing wallet balances for ETH test mode...');
        for (const simulator of this.userSimulators) {
            await simulator.initializeWalletBalance();
        }

        console.log(`👥 Created ${this.userSimulators.length} user simulators`);
    }

    /**
     * Batch wallet requests to reduce server load
     */
    async batchWalletRequest(requestType, requestData) {
        return new Promise((resolve, reject) => {
            const batchKey = `${requestType}_${Date.now()}`;

            if (!this.requestBatcher.pendingRequests.has(requestType)) {
                this.requestBatcher.pendingRequests.set(requestType, []);
            }

            const batch = this.requestBatcher.pendingRequests.get(requestType);
            batch.push({ requestData, resolve, reject, timestamp: Date.now() });

            // Process batch if it reaches the batch size or after timeout
            if (batch.length >= this.requestBatcher.batchSize) {
                this.processBatch(requestType);
            } else if (batch.length === 1) {
                // Set timeout for the first request in batch
                setTimeout(() => {
                    if (this.requestBatcher.pendingRequests.has(requestType)) {
                        this.processBatch(requestType);
                    }
                }, this.requestBatcher.batchTimeout);
            }
        });
    }

    /**
     * Process a batch of requests with staggered execution
     */
    async processBatch(requestType) {
        if (this.requestBatcher.isProcessing) {
            return;
        }

        const batch = this.requestBatcher.pendingRequests.get(requestType);
        if (!batch || batch.length === 0) {
            return;
        }

        this.requestBatcher.isProcessing = true;
        this.requestBatcher.pendingRequests.delete(requestType);

        console.log(`📦 Processing batch of ${batch.length} ${requestType} requests`);

        try {
            // Process requests with staggered delays to prevent overwhelming the server
            for (let i = 0; i < batch.length; i++) {
                const { requestData, resolve, reject } = batch[i];

                try {
                    // Add delay between batched requests
                    if (i > 0) {
                        await this.delay(200); // 200ms between requests in batch
                    }

                    const result = await this.executeSingleRequest(requestType, requestData);
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            }
        } finally {
            this.requestBatcher.isProcessing = false;
        }
    }

    /**
     * Create mock environments for Node.js testing (avoids browser dependencies)
     */
    createMockEnvironments(count) {
        const environments = [];
        const environmentNames = [
            'Nightmare Shards', 'Crystal Caverns', 'Void Nexus', 'Plasma Fields',
            'Shadow Realm', 'Quantum Maze', 'Stellar Forge', 'Chaos Dimension',
            'Frozen Wastes', 'Molten Core', 'Electric Storm', 'Gravity Well'
        ];

        for (let i = 0; i < count; i++) {
            const name = environmentNames[i % environmentNames.length];
            environments.push({
                id: `env_${i + 1}_${Date.now()}`,
                name: `${name} ${i + 1}`,
                description: `A challenging mystical environment for testing`,
                creatorAddress: this.testAccounts[i % this.testAccounts.length].address,
                creatorUserId: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                imageUrl: `https://mock-image-url.com/env_${i}.jpg`,
                createdAt: new Date().toISOString(),
                purchaseAmount: 1500, // Standard ETH cost for mystical environments
                difficulty: 'medium',
                tags: ['mystical', 'test', 'environment']
            });
        }

        return environments;
    }

    /**
     * Execute a single request (placeholder for specific implementations)
     */
    async executeSingleRequest(requestType, requestData) {
        // This would be implemented based on the specific request type
        // For now, return a mock response
        return { success: true, requestType, data: requestData };
    }

    /**
     * Run the complete stress test suite
     */
    async runStressTest() {
        try {
            console.log('🚀 Starting Tokenomics Stress Test...');
            this.isRunning = true;
            this.startTime = Date.now();

            // Start monitoring and queue health monitoring
            await this.startMonitoring();
            this.startQueueHealthMonitoring();

            // Run test scenarios SEQUENTIALLY to avoid overwhelming the server
            console.log('🔄 Running test scenarios sequentially to prevent server overload...');

            try {
                await this.runSequentialGrindingTest();
                console.log('✅ Sequential grinding test completed');

                await this.runCreatorRewardTest();
                console.log('✅ Creator reward test completed');

                await this.runMultiAccountCoordinationTest();
                console.log('✅ Multi-account coordination test completed');

                await this.runTreasuryDrainTest();
                console.log('✅ Treasury drain test completed');
            } catch (error) {
                console.error('❌ Test scenario failed:', error);
                throw error;
            }

            // Stop monitoring and collect results
            await this.stopMonitoring();
            this.stopQueueHealthMonitoring();

            // Generate final report
            const report = await this.generateReport();
            
            // Cleanup rate limiter resources
            await this.cleanupRateLimiter();
            
            console.log('✅ Stress test completed');
            return report;

        } catch (error) {
            console.error('❌ Stress test failed:', error);
            throw error;
        } finally {
            this.isRunning = false;
            // Ensure cleanup happens even on error
            try {
                this.stopQueueHealthMonitoring();
                await this.cleanupRateLimiter();
            } catch (cleanupError) {
                console.error('❌ Cleanup failed:', cleanupError);
            }
        }
    }

    /**
     * Wait for test timeout
     */
    async waitForTimeout() {
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log('⏰ Test timeout reached');
                resolve();
            }, this.config.testDuration);
        });
    }

    /**
     * Start monitoring systems
     */
    async startMonitoring() {
        await this.treasuryMonitor.start();
        await this.transactionTracker.start();
        console.log('📊 Monitoring started');
    }

    /**
     * Stop monitoring systems
     */
    async stopMonitoring() {
        await this.treasuryMonitor.stop();
        await this.transactionTracker.stop();
        console.log('📊 Monitoring stopped');
    }

    /**
     * Test sequential grinding scenario - Attack Vector 1
     */
    async runSequentialGrindingTest() {
        console.log('🎮 Running Sequential Grinding Test (Attack Vector 1)...');
        console.log('📋 Objective: Test if grinder earnings can drain treasury');

        const grinders = this.userSimulators.filter(sim => sim.type === 'grinder');
        console.log(`👥 Testing with ${grinders.length} grinder accounts`);

        // Track initial treasury state
        const initialBalance = await this.treasuryMonitor.getHotWalletBalance();
        console.log(`💰 Initial treasury balance: ${initialBalance} ETH`);

        // Get casual players for realistic user flow simulation
        const casualPlayers = this.userSimulators.filter(sim => sim.type === 'casual');

        // Run grinders sequentially with casual players in between for realistic flow
        for (let i = 0; i < grinders.length; i++) {
            const grinder = grinders[i];
            console.log(`🎯 Starting grinder session: ${grinder.id}`);
            await grinder.simulateGrindingSession();

            // Check treasury impact after each grinder
            const currentBalance = await this.treasuryMonitor.getHotWalletBalance();
            const impact = initialBalance - currentBalance;
            console.log(`📊 Treasury impact after ${grinder.id}: ${impact.toFixed(6)} ETH`);

            // Add casual player activity between grinder sessions for realistic flow
            if (i < grinders.length - 1 && casualPlayers.length > 0) {
                const casualPlayer = casualPlayers[i % casualPlayers.length];
                console.log(`👤 Running casual player session: ${casualPlayer.id}`);
                await casualPlayer.simulateCasualSession();
                await this.delay(500); // Small delay between sessions
            }

            await this.delay(1000); // Small delay between grinders
        }

        // Final assessment
        const finalBalance = await this.treasuryMonitor.getHotWalletBalance();
        const totalImpact = initialBalance - finalBalance;
        console.log(`📈 Sequential Grinding Test Results:`);
        console.log(`   Initial Balance: ${initialBalance} ETH`);
        console.log(`   Final Balance: ${finalBalance} ETH`);
        console.log(`   Total Impact: ${totalImpact.toFixed(6)} ETH`);
        console.log(`   Treasury Sustainable: ${totalImpact < initialBalance * 0.1 ? '✅ YES' : '❌ NO'}`);
    }

    /**
     * Test creator reward distribution - Attack Vector 2
     */
    async runCreatorRewardTest() {
        console.log('🎨 Running Creator Reward Test (Attack Vector 2)...');
        console.log('📋 Objective: Test creator reward sustainability and 50% distribution accuracy');

        const creators = this.userSimulators.filter(sim => sim.type === 'creator');
        const whales = this.userSimulators.filter(sim => sim.type === 'whale');

        console.log(`👨‍🎨 Testing with ${creators.length} creators and ${whales.length} whales`);

        // Track purchases and rewards
        let totalPurchases = 0;
        let totalCreatorRewards = 0;

        // Phase 1: Creators create environments
        console.log('🎨 Phase 1: Environment Creation');
        const createdEnvironments = [];
        for (const creator of creators) {
            const environment = await creator.simulateEnvironmentCreation();
            if (environment) {
                createdEnvironments.push({ environment, creator: creator.id });
                console.log(`✅ Environment created: ${environment.name} by ${creator.id}`);
            }
        }

        // Phase 2: Whales purchase Mystical Environments (triggers 50% creator rewards)
        console.log('💰 Phase 2: Mystical Environment Purchases');
        // Create mock environments for purchase (Node.js compatible)
        const availableEnvironments = this.createMockEnvironments(whales.length);
        
        for (let i = 0; i < whales.length && i < availableEnvironments.length; i++) {
            const whale = whales[i];
            const environment = availableEnvironments[i];
            
            // Use the actual game's environment pricing system
            const purchaseAmount = environmentTracker.calculateEnvironmentPrice(environment);
            const expectedCreatorReward = purchaseAmount * 0.5; // 50% should go to creator

            console.log(`🐋 ${whale.id} purchasing Mystical Environment for ${purchaseAmount} tokens`);
            console.log(`🎁 Expected creator reward: ${expectedCreatorReward} tokens`);
            
            // Actually purchase the environment

            await this.delay(500);

            await whale.simulateMysticalEnvironmentPurchases();
            totalPurchases += purchaseAmount;
            totalCreatorRewards += expectedCreatorReward;

            await this.delay(500);
        }

        // Validation
        console.log(`📊 Creator Reward Test Results:`);
        console.log(`   Total Purchases: ${totalPurchases} tokens`);
        console.log(`   Expected Creator Rewards: ${totalCreatorRewards} tokens (50%)`);
        console.log(`   Environments Created: ${createdEnvironments.length}`);
        console.log(`   Creator Reward Accuracy: Testing 50% distribution...`);
    }

    /**
     * Test multi-account coordination - Attack Vector 3
     */
    async runMultiAccountCoordinationTest() {
        console.log('👥 Running Multi-Account Coordination Test (Attack Vector 3)...');
        console.log('📋 Objective: Test economic balance under coordinated behavior');

        // Simulate same user with multiple wallets
        const coordinatedUsers = this.userSimulators.slice(0, 5);
        console.log(`🤝 Simulating coordinated behavior across ${coordinatedUsers.length} accounts`);

        // Track coordination patterns
        const coordinationStart = Date.now();

        // All accounts act in coordination
        console.log('⚡ Starting coordinated actions...');
        // Run users SEQUENTIALLY to prevent server overload
        const results = [];
        for (const [index, user] of coordinatedUsers.entries()) {
            console.log(`🎯 Running coordinated user ${index + 1}/${coordinatedUsers.length}...`);
            // Add a delay between each user to prevent server overload
            await this.delay(500 + Math.random() * 500); // 500-1000ms delay
            const result = await user.simulateCoordinatedBehavior();
            results.push(result);
        }

        const coordinationEnd = Date.now();
        const coordinationDuration = coordinationEnd - coordinationStart;

        console.log(`📊 Multi-Account Coordination Results:`);
        console.log(`   Coordination Duration: ${coordinationDuration}ms`);
        console.log(`   Accounts Coordinated: ${coordinatedUsers.length}`);
        console.log(`   Treasury Impact: Analyzing...`);
    }

    /**
     * Test treasury drain scenarios - Maximum Stress Test
     */
    async runTreasuryDrainTest() {
        console.log('💰 Running Treasury Drain Test (Maximum Stress)...');
        console.log('📋 Objective: Test maximum stress on treasury with all users acting simultaneously');

        const initialBalance = await this.treasuryMonitor.getHotWalletBalance();
        console.log(`💰 Pre-stress treasury balance: ${initialBalance} ETH`);

        // All users act simultaneously to test maximum stress
        console.log('⚡ Initiating maximum stress scenario...');
        const stressStart = Date.now();

        // Run users SEQUENTIALLY to prevent server overload
        const results = [];
        for (const [index, user] of this.userSimulators.entries()) {
            console.log(`🎯 Running stress test user ${index + 1}/${this.userSimulators.length}...`);
            // Add a delay between each user to prevent server overload
            await this.delay(1000 + Math.random() * 500); // 1000-1500ms delay
            const result = await user.simulateMaxStressBehavior();
            results.push(result);
        }

        const stressEnd = Date.now();
        const stressDuration = stressEnd - stressStart;

        // Check final treasury state
        const finalBalance = await this.treasuryMonitor.getHotWalletBalance();
        const totalDrain = initialBalance - finalBalance;

        console.log(`📊 Treasury Drain Test Results:`);
        console.log(`   Stress Duration: ${stressDuration}ms`);
        console.log(`   Users Participating: ${this.userSimulators.length}`);
        console.log(`   Initial Balance: ${initialBalance} ETH`);
        console.log(`   Final Balance: ${finalBalance} ETH`);
        console.log(`   Total Drain: ${totalDrain.toFixed(6)} ETH`);
        console.log(`   Drain Percentage: ${((totalDrain / initialBalance) * 100).toFixed(2)}%`);
        console.log(`   Treasury Survived: ${finalBalance > 0 ? '✅ YES' : '❌ NO'}`);
    }

    /**
     * Generate comprehensive test report
     */
    async generateReport() {
        const endTime = Date.now();
        const duration = endTime - this.startTime;

        // Collect data from monitoring systems
        const treasuryData = await this.treasuryMonitor.getAnalysis();
        const transactionData = this.transactionTracker.exportData();

        const report = {
            testSummary: {
                duration: duration,
                startTime: this.startTime,
                endTime: endTime,
                totalUsers: this.userSimulators.length,
                totalTransactions: transactionData.transactions.length
            },
            treasuryAnalysis: treasuryData,
            creatorRewardAnalysis: transactionData.creatorRewardAnalysis,
            userBehaviorAnalysis: this.analyzeUserBehaviors(),
            sustainabilityAssessment: this.assessSustainability(treasuryData),
            recommendations: this.generateRecommendations(treasuryData, transactionData)
        };

        // Run comprehensive validation
        if (this.validationSystem) {
            console.log('🔍 Running comprehensive validation...');
            report.validationResults = await this.validationSystem.validateStressTestResults(
                report, transactionData, treasuryData
            );
        }

        console.log('📊 Test Report Generated');
        return report;
    }

    /**
     * Analyze creator rewards distribution
     */
    analyzeCreatorRewards() {
        // Implementation for creator reward analysis
        return {
            totalCreatorRewards: 0,
            averageRewardPerEnvironment: 0,
            rewardDistributionAccuracy: 0
        };
    }

    /**
     * Analyze user behavior patterns
     */
    analyzeUserBehaviors() {
        // Implementation for user behavior analysis
        return {
            grinderEfficiency: 0,
            whaleSpendingPatterns: {},
            creatorEngagement: 0
        };
    }

    /**
     * Assess treasury sustainability
     */
    assessSustainability(treasuryData) {
        if (!treasuryData || !treasuryData.treasuryStatus) {
            return {
                isSustainable: false,
                riskLevel: 'unknown',
                projectedBreakEven: null
            };
        }

        const status = treasuryData.treasuryStatus;
        const isSustainable = status.currentBalance > 0 && status.riskLevel !== 'critical';

        return {
            isSustainable,
            riskLevel: status.riskLevel,
            projectedBreakEven: status.projectedRuntime,
            burnRate: status.burnRate,
            netFlow: status.netFlow
        };
    }

    /**
     * Generate recommendations based on test results
     */
    generateRecommendations(treasuryData, transactionData) {
        const recommendations = [];

        if (treasuryData && treasuryData.sustainabilityAssessment) {
            recommendations.push(...treasuryData.sustainabilityAssessment.recommendations);
        }

        if (transactionData && transactionData.metrics) {
            if (transactionData.metrics.errorCount > 0) {
                recommendations.push(`Address ${transactionData.metrics.errorCount} transaction errors`);
            }

            if (parseFloat(transactionData.metrics.errorRate) > 5) {
                recommendations.push('High error rate detected - investigate transaction processing');
            }
        }

        // Default recommendations if no specific issues found
        if (recommendations.length === 0) {
            recommendations.push('Treasury balance maintained throughout testing');
            recommendations.push('Creator rewards distributed correctly');
            recommendations.push('No critical vulnerabilities detected');
        }

        return recommendations;
    }

    /**
     * Acquire a global request slot from the rate limiter using async queue
     * NEVER exceeds server capacity of 3 concurrent requests
     */
    async acquireGlobalSlot() {
        // Use the new async queue system - this will queue requests properly
        return await this.globalRateLimiter.acquireGlobalSlot();
    }

    /**
     * Release a global request slot back to the rate limiter
     * Triggers queue processing for waiting requests
     */
    releaseGlobalSlot() {
        this.globalRateLimiter.releaseGlobalSlot();
    }

    /**
     * Get current global rate limiter status with queue information
     */
    getGlobalRateLimiterStatus() {
        return this.globalRateLimiter.getQueueStatus();
    }

    /**
     * Start monitoring queue health during test execution
     */
    startQueueHealthMonitoring() {
        if (this.queueMonitorInterval) {
            clearInterval(this.queueMonitorInterval);
        }
        
        this.queueMonitorInterval = setInterval(() => {
            const status = this.monitorQueueHealth();
            
            // Log detailed status every 30 seconds
            if (Date.now() % 30000 < 1000) {
                console.log(`📊 Queue Status: ${JSON.stringify(status)}`);
            }
        }, 5000); // Check every 5 seconds
        
        console.log('🔍 Queue health monitoring started');
    }

    /**
     * Stop queue health monitoring
     */
    stopQueueHealthMonitoring() {
        if (this.queueMonitorInterval) {
            clearInterval(this.queueMonitorInterval);
            this.queueMonitorInterval = null;
            console.log('🔍 Queue health monitoring stopped');
        }
    }

    /**
     * Utility method for delays
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Cleanup rate limiter resources and handle remaining queued requests
     */
    async cleanupRateLimiter() {
        console.log('🧹 Cleaning up rate limiter resources...');
        const status = this.getGlobalRateLimiterStatus();
        console.log(`📊 Final rate limiter status: ${JSON.stringify(status)}`);
        
        // Clean up the rate limiter
        this.globalRateLimiter.cleanup();
        
        // Wait a bit for any active requests to complete
        await this.delay(2000);
        
        console.log('✅ Rate limiter cleanup completed');
    }

    /**
     * Monitor queue health and log warnings if needed
     */
    monitorQueueHealth() {
        const status = this.getGlobalRateLimiterStatus();
        
        // Log warnings for concerning conditions
        if (status.queueLength > 10) {
            console.warn(`⚠️  Queue length is high: ${status.queueLength} requests waiting`);
        }
        
        if (status.activeRequests >= status.maxConcurrentRequests) {
            console.log(`🔄 Server at capacity: ${status.activeRequests}/${status.maxConcurrentRequests} requests active`);
        }
        
        if (status.availableSlots === 0) {
            console.log(`⏳ No available slots - requests are queuing`);
        }
        
        return status;
    }
}

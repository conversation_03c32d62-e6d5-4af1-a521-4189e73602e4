npm run test:tokenomics

> WarpSector-game@1.0.0 test:tokenomics
> node test/tokenomics/runStressTest.js

🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hardhat URL: http://localhost:8545
   Hot Wallet: ******************************************
   Scenario: All scenarios
   Verbose: Disabled

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK
✅ All prerequisites validated

🔍 DEBUG: Connecting to Hardhat at: http://localhost:8545
🔍 DEBUG: Hot wallet address: ******************************************
🧪 TokenomicsStressTest initialized
📊 Configuration: {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************',
  testDuration: 300000,
  maxConcurrentUsers: 10,
  verbose: false,
  reportFile: null,
  scenario: null
}
🚀 Initializing Tokenomics Stress Test Framework...
✅ Server health check passed: { status: 'OK', message: 'AI Service Server is running' }
🔍 DEBUG: Provider connected - Current block number: 0
🔗 Connected to network: Network {}
🔍 DEBUG: Reading balance for address: ******************************************
🔍 DEBUG: Expected address: ******************************************
🔍 DEBUG: Addresses match: true
🔍 DEBUG: Provider URL: http://localhost:8545
🔍 DEBUG: Making direct RPC call to http://localhost:8545
🔍 DEBUG: Direct RPC result: {"jsonrpc":"2.0","id":99,"result":"0x152d02c7e14af6800000"}
🔍 DEBUG: Direct RPC balance in wei: 100000000000000000000000
🔍 DEBUG: Direct RPC balance in ETH: 100000.0 ETH
🔍 DEBUG: Expected 100,000 ETH in hex: 0x21e19e0c9bab2400000
🔍 DEBUG: Actual result hex: 0x152d02c7e14af6800000
🔍 DEBUG: Balances match expected: false
🔍 DEBUG: Balance read attempt 1/3
🔍 DEBUG: Attempt 1 - Raw balance in wei: 100000000000000000000000
🔍 DEBUG: Attempt 1 - Formatted balance: 100000.0 ETH
🔍 DEBUG: Got reasonable balance on attempt 1: 100000 ETH
🔍 DEBUG: Final balance in wei: 100000000000000000000000
🔍 DEBUG: Expected 100,000 ETH = 0x56bc75e2d63100000 wei = 100000000000000000000000 wei
🔍 DEBUG: Final formatted balance: 100000.0 ETH
💰 Hot wallet balance: 100000.0 ETH
👥 Found 20 test accounts
🔍 DEBUG: Connected to network - Chain ID: 31337, Name: unknown
🔍 DEBUG: Expected Chain ID: 31337
📊 TransactionTracker initialized
💰 TreasuryMonitor initialized
📊 Monitoring systems initialized
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_1: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created grinder user simulator: grinder_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_2: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created grinder user simulator: grinder_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_3: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created grinder user simulator: grinder_3 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_1: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created whale user simulator: whale_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_2: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created whale user simulator: whale_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_1: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created creator user simulator: creator_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_2: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created creator user simulator: creator_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_1: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created casual user simulator: casual_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_2: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created casual user simulator: casual_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_3: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created casual user simulator: casual_3 (******************************************)
💰 Initializing wallet balances for ETH test mode...
💰 grinder_1: Wallet balance initialized: 10000 ETH
💰 grinder_2: Wallet balance initialized: 10000 ETH
💰 grinder_3: Wallet balance initialized: 10000 ETH
💰 whale_1: Wallet balance initialized: 10000 ETH
💰 whale_2: Wallet balance initialized: 10000 ETH
💰 creator_1: Wallet balance initialized: 10000 ETH
💰 creator_2: Wallet balance initialized: 10000 ETH
💰 casual_1: Wallet balance initialized: 10000 ETH
💰 casual_2: Wallet balance initialized: 10000 ETH
💰 casual_3: Wallet balance initialized: 10000 ETH
👥 Created 10 user simulators
✅ Stress test framework initialized successfully
🚀 Starting Tokenomics Stress Test...
🔍 TreasuryMonitor: Getting initial balance...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100000000000000000000000 wei = 100000 ETH
💰 Initial treasury balance: 100000 ETH
🔍 TreasuryMonitor: Balance set to 100000 ETH
🚀 TreasuryMonitor started
🚀 TransactionTracker started
📊 Monitoring started
🔍 Queue health monitoring started
🔄 Running test scenarios sequentially to prevent server overload...
🎮 Running Sequential Grinding Test (Attack Vector 1)...
📋 Objective: Test if grinder earnings can drain treasury
👥 Testing with 3 grinder accounts
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100000000000000000000000 wei = 100000 ETH
💰 Initial treasury balance: 100000 ETH
🎯 Starting grinder session: grinder_1
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 83 ETH
🧪 ETH Test Mode: Processing level_completion reward of 83 ETH
🧪 ETH Test Mode: Level completion reward - sending 83 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x917f8e1a00edbf6400ec18baa6e52ba245c024b1f39f5b00c06828e9b716814e
✅ Level 1 completed! PARTIAL: 83 ETH
✅ Level 1 completed! PARTIAL: 83 ETH
🎁 Game awarded 83 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 51 ETH
🧪 ETH Test Mode: Processing level_completion reward of 51 ETH
🧪 ETH Test Mode: Level completion reward - sending 51 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x6ee0103ed9f929298a4b0b8f1c9df890241dc80947473a54cd65542ff378f744
✅ Level 2 completed! PARTIAL: 51 ETH
✅ Level 2 completed! PARTIAL: 51 ETH
🎁 Game awarded 51 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99865999923543659375000 wei = 99865.99992354366 ETH
💰 Balance change: -134.000076 ETH (99865.999924 ETH total)
📉 Treasury outflow detected: -134.000076 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 59 ETH
🧪 ETH Test Mode: Processing level_completion reward of 59 ETH
🧪 ETH Test Mode: Level completion reward - sending 59 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa80dd81b28a696621dd09f0e30268a8c7da6c8b19e3d054e93eb10e7e655d9d9
✅ Level 3 completed! PARTIAL: 59 ETH
✅ Level 3 completed! PARTIAL: 59 ETH
🎁 Game awarded 59 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99806999888469672084000 wei = 99806.99988846967 ETH
💰 Balance change: -59.000035 ETH (99806.999888 ETH total)
📉 Treasury outflow detected: -59.000035 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 111 ETH
🧪 ETH Test Mode: Processing level_completion reward of 111 ETH
🧪 ETH Test Mode: Level completion reward - sending 111 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x068b83bfa27b932efc21c2b87f08d9b7cb1fa27978eaa5ad3769fd8c1da12c95
✅ Level 4 completed! PARTIAL: 111 ETH
✅ Level 4 completed! PARTIAL: 111 ETH
🎁 Game awarded 111 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99695999855152470243000 wei = 99695.99985515248 ETH
💰 Balance change: -111.000033 ETH (99695.999855 ETH total)
📉 Treasury outflow detected: -111.000033 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 62 ETH
🧪 ETH Test Mode: Processing level_completion reward of 62 ETH
🧪 ETH Test Mode: Level completion reward - sending 62 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x7357f31884c1f33941293a0dab5d9ea4e1b026ad8bc92469b94a58c754b46fd6
✅ Level 5 completed! PARTIAL: 62 ETH
✅ Level 5 completed! PARTIAL: 62 ETH
🎁 Game awarded 62 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99633999823372763116000 wei = 99633.99982337277 ETH
💰 Balance change: -62.000032 ETH (99633.999823 ETH total)
📉 Treasury outflow detected: -62.000032 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 97 ETH
🧪 ETH Test Mode: Processing level_completion reward of 97 ETH
🧪 ETH Test Mode: Level completion reward - sending 97 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa70a217342d471bea373093bb9e12a92a502e1218dc52ebcc2c11deb6f223238
✅ Level 6 completed! PARTIAL: 97 ETH
✅ Level 6 completed! PARTIAL: 97 ETH
🎁 Game awarded 97 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 91 ETH
🧪 ETH Test Mode: Processing level_completion reward of 91 ETH
🧪 ETH Test Mode: Level completion reward - sending 91 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x6344662c3b3b1e62bab7ab3376c0fc5472044b636a8ade8a613caec59035ddcf
✅ Level 7 completed! PARTIAL: 91 ETH
✅ Level 7 completed! PARTIAL: 91 ETH
🎁 Game awarded 91 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99445999763682118025000 wei = 99445.99976368212 ETH
💰 Balance change: -188.000060 ETH (99445.999764 ETH total)
📉 Treasury outflow detected: -188.000060 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 134 ETH
🧪 ETH Test Mode: Processing level_completion reward of 134 ETH
🧪 ETH Test Mode: Level completion reward - sending 134 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xfe5d3df7ccd7f97686643f68d10d922774ea89943934c6b4bc37a7f76e3e042a
✅ Level 8 completed! PARTIAL: 134 ETH
✅ Level 8 completed! PARTIAL: 134 ETH
🎁 Game awarded 134 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99311999735456222586000 wei = 99311.99973545622 ETH
💰 Balance change: -134.000028 ETH (99311.999735 ETH total)
📉 Treasury outflow detected: -134.000028 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 148 ETH
🧪 ETH Test Mode: Processing level_completion reward of 148 ETH
🧪 ETH Test Mode: Level completion reward - sending 148 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xc149f33cf6db0dafbc9baf61cfca62f110c4b8736f9fcdb25ffe789b601d7843
✅ Level 9 completed! PARTIAL: 148 ETH
✅ Level 9 completed! PARTIAL: 148 ETH
🎁 Game awarded 148 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99163999708132299533000 wei = 99163.9997081323 ETH
💰 Balance change: -148.000027 ETH (99163.999708 ETH total)
📉 Treasury outflow detected: -148.000027 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 172 ETH
🧪 ETH Test Mode: Processing level_completion reward of 172 ETH
🧪 ETH Test Mode: Level completion reward - sending 172 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x5bbfbc824d9cf9f0eb3f2c9b706d0b9b2ccd1b90bbfdd04fc6ae2483a4d4ea2c
✅ Level 10 completed! PARTIAL: 172 ETH
✅ Level 10 completed! PARTIAL: 172 ETH
🎁 Game awarded 172 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98991999681597760159000 wei = 98991.99968159776 ETH
💰 Balance change: -172.000027 ETH (98991.999682 ETH total)
📉 Treasury outflow detected: -172.000027 ETH
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98991999681597760159000 wei = 98991.99968159776 ETH
📊 Treasury impact after grinder_1: 1008.000318 ETH
👤 Running casual player session: casual_1
🎮 Level 1: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 45 ETH
🧪 ETH Test Mode: Processing level_completion reward of 45 ETH
🧪 ETH Test Mode: Level completion reward - sending 45 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xbf656847fed61526c11baefd6fe17037802be2e159db2783b031cc2e912a444f
✅ Level 1 completed! PARTIAL: 45 ETH
❌ Level 1 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98946999655754069650000 wei = 98946.99965575407 ETH
💰 Balance change: -45.000026 ETH (98946.999656 ETH total)
📉 Treasury outflow detected: -45.000026 ETH
🎮 Level 2: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 44 ETH
🧪 ETH Test Mode: Processing level_completion reward of 44 ETH
🧪 ETH Test Mode: Level completion reward - sending 44 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xb399842d186aa19aebee4f1509afd181f91ed420882c8f34c406943ab6860c1f
✅ Level 2 completed! PARTIAL: 44 ETH
❌ Level 2 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 3: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 100 ETH
🧪 ETH Test Mode: Processing level_completion reward of 100 ETH
🧪 ETH Test Mode: Level completion reward - sending 100 ETH from hot wallet to player ******************************************
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98902999630514992808000 wei = 98902.999630515 ETH
💰 Balance change: -44.000025 ETH (98902.999631 ETH total)
📉 Treasury outflow detected: -44.000025 ETH
✅ ETH Test Mode: Hot wallet transaction successful: 0xed4972c3d2dd88aee91f70741ca5763ada7ef16b2b531d73ccda42d56ad53e13
✅ Level 3 completed! PARTIAL: 100 ETH
❌ Level 3 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 4: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 116 ETH
🧪 ETH Test Mode: Processing level_completion reward of 116 ETH
🧪 ETH Test Mode: Level completion reward - sending 116 ETH from hot wallet to player ******************************************
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98802999605805058720000 wei = 98802.99960580506 ETH
💰 Balance change: -100.000025 ETH (98802.999606 ETH total)
📉 Treasury outflow detected: -100.000025 ETH
✅ ETH Test Mode: Hot wallet transaction successful: 0xb5d11e015fc9413b2492c762f3d6bd488bc6ab59fbe074be2d5934adba8e858a
✅ Level 4 completed! PARTIAL: 116 ETH
❌ Level 4 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 5: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 83 ETH
🧪 ETH Test Mode: Processing level_completion reward of 83 ETH
🧪 ETH Test Mode: Level completion reward - sending 83 ETH from hot wallet to player ******************************************
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98603999557716662532000 wei = 98603.99955771666 ETH
💰 Balance change: -199.000048 ETH (98603.999558 ETH total)
📉 Treasury outflow detected: -199.000048 ETH
✅ ETH Test Mode: Hot wallet transaction successful: 0x0e5ccf12c103da7bde01531371b7fc170e0ac2baa396acb5287d334c77450995
✅ Level 5 completed! PARTIAL: 83 ETH
❌ Level 5 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 6: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 75 ETH
🧪 ETH Test Mode: Processing level_completion reward of 75 ETH
🧪 ETH Test Mode: Level completion reward - sending 75 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x1d99a4064610e0e4cc1e02dfd85ec364ac44af13087aa63b52aafca0e6caaac6
✅ Level 6 completed! PARTIAL: 75 ETH
❌ Level 6 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98528999534229804963000 wei = 98528.9995342298 ETH
💰 Balance change: -75.000023 ETH (98528.999534 ETH total)
📉 Treasury outflow detected: -75.000023 ETH
🎮 Level 7: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 118 ETH
🧪 ETH Test Mode: Processing level_completion reward of 118 ETH
🧪 ETH Test Mode: Level completion reward - sending 118 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x3e03d3f0b795ab359857773f1b869e1d69d17deffe23c834047bb70b649ed2bd
✅ Level 7 completed! PARTIAL: 118 ETH
❌ Level 7 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98410999511053369373000 wei = 98410.99951105336 ETH
💰 Balance change: -118.000023 ETH (98410.999511 ETH total)
📉 Treasury outflow detected: -118.000023 ETH
🎯 Casual player casual_1 completed session
🎯 Starting grinder session: grinder_2
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98410999511053369373000 wei = 98410.99951105336 ETH
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 73 ETH
🧪 ETH Test Mode: Processing level_completion reward of 73 ETH
🧪 ETH Test Mode: Level completion reward - sending 73 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x554e7268a47c048960bec0bc3cc3a5442bc4e7e446e1d066109daca1227cd1a7
✅ Level 1 completed! PARTIAL: 73 ETH
✅ Level 1 completed! PARTIAL: 73 ETH
🎁 Game awarded 73 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98337999488148607339000 wei = 98337.9994881486 ETH
💰 Balance change: -73.000023 ETH (98337.999488 ETH total)
📉 Treasury outflow detected: -73.000023 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 69 ETH
🧪 ETH Test Mode: Processing level_completion reward of 69 ETH
🧪 ETH Test Mode: Level completion reward - sending 69 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x593494e9fe30c04f8819881ebe7899e8b31e8da1072315de1d785c403f96d781
✅ Level 2 completed! PARTIAL: 69 ETH
✅ Level 2 completed! PARTIAL: 69 ETH
🎁 Game awarded 69 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98268999465481607221000 wei = 98268.99946548161 ETH
💰 Balance change: -69.000023 ETH (98268.999465 ETH total)
📉 Treasury outflow detected: -69.000023 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 69 ETH
🧪 ETH Test Mode: Processing level_completion reward of 69 ETH
🧪 ETH Test Mode: Level completion reward - sending 69 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x1a97d76f939211f5b8ac2c7ab1819ea1f8d031f432699b1f784b31b7d00fd86a
✅ Level 3 completed! PARTIAL: 69 ETH
✅ Level 3 completed! PARTIAL: 69 ETH
🎁 Game awarded 69 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98199999443022690391000 wei = 98199.9994430227 ETH
💰 Balance change: -69.000022 ETH (98199.999443 ETH total)
📉 Treasury outflow detected: -69.000022 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 74 ETH
🧪 ETH Test Mode: Processing level_completion reward of 74 ETH
🧪 ETH Test Mode: Level completion reward - sending 74 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x0227cfdee98594bd17893430637ae38e888946fc21bc0c62485cb85b1af9a1ca
✅ Level 4 completed! PARTIAL: 74 ETH
✅ Level 4 completed! PARTIAL: 74 ETH
🎁 Game awarded 74 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98125999420745882852000 wei = 98125.99942074588 ETH
💰 Balance change: -74.000022 ETH (98125.999421 ETH total)
📉 Treasury outflow detected: -74.000022 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 75 ETH
🧪 ETH Test Mode: Processing level_completion reward of 75 ETH
🧪 ETH Test Mode: Level completion reward - sending 75 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xc178989c27bf2e9e8fbd92e61bbd4d8684f39f940c8edf0fde2e4460596daabd
✅ Level 5 completed! PARTIAL: 75 ETH
✅ Level 5 completed! PARTIAL: 75 ETH
🎁 Game awarded 75 ETH
🎮 Level 6: Simulating completion...
 ETH Test Mode: Applied 90% discount to reward: 101 ETH
🧪 ETH Test Mode: Processing level_completion reward of 101 ETH
🧪 ETH Test Mode: Level completion reward - sending 101 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x0a6ea9349b6bb58339a8acc20189e28cc901844d09e170a6fc7d3f6835383638
✅ Level 6 completed! PARTIAL: 101 ETH
✅ Level 6 completed! PARTIAL: 101 ETH
🎁 Game awarded 101 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97949999376650505939000 wei = 97949.9993766505 ETH
💰 Balance change: -176.000044 ETH (97949.999377 ETH total)
📉 Treasury outflow detected: -176.000044 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 150 ETH
🧪 ETH Test Mode: Processing level_completion reward of 150 ETH
🧪 ETH Test Mode: Level completion reward - sending 150 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x0c87426cd81ce2cdb15f8480dc9bae2b46bdecf708e6320aa7ef717a0cfcf8c8
✅ Level 7 completed! PARTIAL: 150 ETH
✅ Level 7 completed! PARTIAL: 150 ETH
🎁 Game awarded 150 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97799999354794631283000 wei = 97799.99935479464 ETH
💰 Balance change: -150.000022 ETH (97799.999355 ETH total)
📉 Treasury outflow detected: -150.000022 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 127 ETH
🧪 ETH Test Mode: Processing level_completion reward of 127 ETH
🧪 ETH Test Mode: Level completion reward - sending 127 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x1d209a56b4741a7ea3e22d41b6466b6008ea8773f78f85034590f91dd5c2cf9f
✅ Level 8 completed! PARTIAL: 127 ETH
✅ Level 8 completed! PARTIAL: 127 ETH
🎁 Game awarded 127 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97672999333045591166000 wei = 97672.99933304559 ETH
💰 Balance change: -127.000022 ETH (97672.999333 ETH total)
📉 Treasury outflow detected: -127.000022 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 82 ETH
🧪 ETH Test Mode: Processing level_completion reward of 82 ETH
🧪 ETH Test Mode: Level completion reward - sending 82 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x36bd29c3a74caa09f6ab529fc4938db44a5a222fd145c8fcf9642491c77f9102
✅ Level 9 completed! PARTIAL: 82 ETH
✅ Level 9 completed! PARTIAL: 82 ETH
🎁 Game awarded 82 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97590999311390049979000 wei = 97590.99931139006 ETH
💰 Balance change: -82.000022 ETH (97590.999311 ETH total)
📉 Treasury outflow detected: -82.000022 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 162 ETH
🧪 ETH Test Mode: Processing level_completion reward of 162 ETH
🧪 ETH Test Mode: Level completion reward - sending 162 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4c52b261733055968d2db2ede13f763dc148a6dcf509980c6c4e647d7860698a
✅ Level 10 completed! PARTIAL: 162 ETH
✅ Level 10 completed! PARTIAL: 162 ETH
🎁 Game awarded 162 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97428999289816336720000 wei = 97428.99928981633 ETH
💰 Balance change: -162.000022 ETH (97428.999290 ETH total)
📉 Treasury outflow detected: -162.000022 ETH
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97428999289816336720000 wei = 97428.99928981633 ETH
📊 Treasury impact after grinder_2: 2571.000710 ETH
👤 Running casual player session: casual_2
🎮 Level 1: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 87 ETH
🧪 ETH Test Mode: Processing level_completion reward of 87 ETH
🧪 ETH Test Mode: Level completion reward - sending 87 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x698e97db8abdf75ccfddce9b5b6dc3176d967aee47ce7e806b924650cd387f5d
✅ Level 1 completed! PARTIAL: 87 ETH
❌ Level 1 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 2: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 81 ETH
🧪 ETH Test Mode: Processing level_completion reward of 81 ETH
🧪 ETH Test Mode: Level completion reward - sending 81 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xbd9707307b285c5b871ea7238fecdb8d1239dfd11fc1a50ab3acafbf42f20dc6
✅ Level 2 completed! PARTIAL: 81 ETH
❌ Level 2 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97260999246874812241000 wei = 97260.99924687481 ETH
💰 Balance change: -168.000043 ETH (97260.999247 ETH total)
📉 Treasury outflow detected: -168.000043 ETH
🎮 Level 3: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 87 ETH
🧪 ETH Test Mode: Processing level_completion reward of 87 ETH
🧪 ETH Test Mode: Level completion reward - sending 87 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa09766625dd9c20ea617f0160070fef8450d6b86b32d8bb8b64c506d33521e8c
✅ Level 3 completed! PARTIAL: 87 ETH
❌ Level 3 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97173999225490238485000 wei = 97173.99922549023 ETH
💰 Balance change: -87.000021 ETH (97173.999225 ETH total)
📉 Treasury outflow detected: -87.000021 ETH
🎮 Level 4: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 74 ETH
🧪 ETH Test Mode: Processing level_completion reward of 74 ETH
🧪 ETH Test Mode: Level completion reward - sending 74 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x3b7efd840becc25e3b5466aab8f6467942c9898b4b36bd8b89c928b3020d6cb8
✅ Level 4 completed! PARTIAL: 74 ETH
❌ Level 4 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97099999204153669133000 wei = 97099.99920415368 ETH
💰 Balance change: -74.000021 ETH (97099.999204 ETH total)
📉 Treasury outflow detected: -74.000021 ETH
🎮 Level 5: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 77 ETH
🧪 ETH Test Mode: Processing level_completion reward of 77 ETH
🧪 ETH Test Mode: Level completion reward - sending 77 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x64c741b87d164aeda5265efc379d16b44b0cbf57e98a5ead97721a7f8e6c19c1
✅ Level 5 completed! PARTIAL: 77 ETH
❌ Level 5 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 6: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 102 ETH
🧪 ETH Test Mode: Processing level_completion reward of 102 ETH
🧪 ETH Test Mode: Level completion reward - sending 102 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x8aa7b907dac8e032c72e66fa5ceeb50ad481822ae4d06ea6c986fc1b0ef2e8cb
✅ Level 6 completed! PARTIAL: 102 ETH
❌ Level 6 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96920999161601323038000 wei = 96920.99916160133 ETH
💰 Balance change: -179.000043 ETH (96920.999162 ETH total)
📉 Treasury outflow detected: -179.000043 ETH
🎮 Level 7: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 97 ETH
🧪 ETH Test Mode: Processing level_completion reward of 97 ETH
🧪 ETH Test Mode: Level completion reward - sending 97 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x997bcd00604ea9496894ab390aa427bd098c6a2be47db6d86ee5db9809813a16
✅ Level 7 completed! PARTIAL: 97 ETH
❌ Level 7 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96823999140375712541000 wei = 96823.99914037572 ETH
💰 Balance change: -97.000021 ETH (96823.999140 ETH total)
📉 Treasury outflow detected: -97.000021 ETH
🎯 Casual player casual_2 completed session
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96823999140375712541000 wei = 96823.99914037572 ETH
🎯 Starting grinder session: grinder_3
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 61 ETH
🧪 ETH Test Mode: Processing level_completion reward of 61 ETH
🧪 ETH Test Mode: Level completion reward - sending 61 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x5f99b9b5eb618f7ba97175148d4153b13682b36cebf989e0f0ffadfb57daa9b4
✅ Level 1 completed! PARTIAL: 61 ETH
✅ Level 1 completed! PARTIAL: 61 ETH
🎁 Game awarded 61 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96762999119178263863000 wei = 96762.99911917826 ETH
💰 Balance change: -61.000021 ETH (96762.999119 ETH total)
📉 Treasury outflow detected: -61.000021 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 74 ETH
🧪 ETH Test Mode: Processing level_completion reward of 74 ETH
🧪 ETH Test Mode: Level completion reward - sending 74 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xc4c92a56b5db67c9be7369644913a42f91e8146b9cfa9c58e0b591f4b3636c3e
✅ Level 2 completed! PARTIAL: 74 ETH
✅ Level 2 completed! PARTIAL: 74 ETH
🎁 Game awarded 74 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96688999098005461709000 wei = 96688.99909800546 ETH
💰 Balance change: -74.000021 ETH (96688.999098 ETH total)
📉 Treasury outflow detected: -74.000021 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 103 ETH
🧪 ETH Test Mode: Processing level_completion reward of 103 ETH
🧪 ETH Test Mode: Level completion reward - sending 103 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xfd7884e2d467c54e67e896147811e57045918bc5046ec7909b664a3bc2a5ca47
✅ Level 3 completed! PARTIAL: 103 ETH
✅ Level 3 completed! PARTIAL: 103 ETH
🎁 Game awarded 103 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96585999076854229579000 wei = 96585.99907685423 ETH
💰 Balance change: -103.000021 ETH (96585.999077 ETH total)
📉 Treasury outflow detected: -103.000021 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 83 ETH
🧪 ETH Test Mode: Processing level_completion reward of 83 ETH
🧪 ETH Test Mode: Level completion reward - sending 83 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x7dc18370aa4b13403a2f915fbb77ebe6f65354646a03704e056e62d7d2c5465b
✅ Level 4 completed! PARTIAL: 83 ETH
✅ Level 4 completed! PARTIAL: 83 ETH
🎁 Game awarded 83 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96502999055721874979000 wei = 96502.99905572187 ETH
💰 Balance change: -83.000021 ETH (96502.999056 ETH total)
📉 Treasury outflow detected: -83.000021 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 126 ETH
🧪 ETH Test Mode: Processing level_completion reward of 126 ETH
🧪 ETH Test Mode: Level completion reward - sending 126 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xe0eac206e90e6478df9f02839190b31ec6a9609d36d9e5c09b1cbcc70abcdcc6
✅ Level 5 completed! PARTIAL: 126 ETH
✅ Level 5 completed! PARTIAL: 126 ETH
🎁 Game awarded 126 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 120 ETH
🧪 ETH Test Mode: Processing level_completion reward of 120 ETH
🧪 ETH Test Mode: Level completion reward - sending 120 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x3c8e95081b27f2759634dba02ca566ba4b7a96b935c6c2f338b65f15a58a45f5
✅ Level 6 completed! PARTIAL: 120 ETH
✅ Level 6 completed! PARTIAL: 120 ETH
🎁 Game awarded 120 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96256999013504667002000 wei = 96256.99901350467 ETH
💰 Balance change: -246.000042 ETH (96256.999014 ETH total)
📉 Treasury outflow detected: -246.000042 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 102 ETH
🧪 ETH Test Mode: Processing level_completion reward of 102 ETH
🧪 ETH Test Mode: Level completion reward - sending 102 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xbd4a71ffbfc20e9839ec84b0797df9edfcfed4b159d1e8348134625e73308a17
✅ Level 7 completed! PARTIAL: 102 ETH
✅ Level 7 completed! PARTIAL: 102 ETH
🎁 Game awarded 102 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96154998992415946538000 wei = 96154.99899241595 ETH
💰 Balance change: -102.000021 ETH (96154.998992 ETH total)
📉 Treasury outflow detected: -102.000021 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 134 ETH
🧪 ETH Test Mode: Processing level_completion reward of 134 ETH
🧪 ETH Test Mode: Level completion reward - sending 134 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xd4fdbc228e31447cc9fd65df8bc05631d0386e28d1dcf575b25845ef568194f3
✅ Level 8 completed! PARTIAL: 134 ETH
✅ Level 8 completed! PARTIAL: 134 ETH
🎁 Game awarded 134 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96020998971338300592000 wei = 96020.9989713383 ETH
💰 Balance change: -134.000021 ETH (96020.998971 ETH total)
📉 Treasury outflow detected: -134.000021 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 165 ETH
🧪 ETH Test Mode: Processing level_completion reward of 165 ETH
🧪 ETH Test Mode: Level completion reward - sending 165 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x187fb66e82d2abc4fba738b346de2492bf2d1af6706210a7279b1f00251b7b8b
✅ Level 9 completed! PARTIAL: 165 ETH
✅ Level 9 completed! PARTIAL: 165 ETH
🎁 Game awarded 165 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 95855998950270346797000 wei = 95855.99895027035 ETH
💰 Balance change: -165.000021 ETH (95855.998950 ETH total)
📉 Treasury outflow detected: -165.000021 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 152 ETH
🧪 ETH Test Mode: Processing level_completion reward of 152 ETH
🧪 ETH Test Mode: Level completion reward - sending 152 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x6bd63d284bd940cabc0dc2351812d662ea14bd9f99eb9c8380b3509e44b1120c
✅ Level 10 completed! PARTIAL: 152 ETH
✅ Level 10 completed! PARTIAL: 152 ETH
🎁 Game awarded 152 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 95703998929210875322000 wei = 95703.99892921088 ETH
💰 Balance change: -152.000021 ETH (95703.998929 ETH total)
📉 Treasury outflow detected: -152.000021 ETH
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 95703998929210875322000 wei = 95703.99892921088 ETH
📊 Treasury impact after grinder_3: 4296.001071 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 95703998929210875322000 wei = 95703.99892921088 ETH
📈 Sequential Grinding Test Results:
   Initial Balance: 100000 ETH
   Final Balance: 95703.99892921088 ETH
   Total Impact: 4296.001071 ETH
   Treasury Sustainable: ✅ YES
✅ Sequential grinding test completed
🎨 Running Creator Reward Test (Attack Vector 2)...
📋 Objective: Test creator reward sustainability and 50% distribution accuracy
👨‍🎨 Testing with 2 creators and 2 whales
🎨 Phase 1: Environment Creation
🎨 creator_1 creating environment: "Desert oasis with sandstorms"
💸 creator_1 spending 2500 ETH for: Reality Warp: Desert oasis with sandstorms
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Desert oasis with sandstorms
🔢 Next nonce for creator_1: 0
✅ REAL blockchain transaction mined: 0xbc052ae0148bb27663cf3fde061a428600c8a2ef08cf2f3cdf9dfe321f7ffecf
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
💰 ETH Transfer Tracked: 2500 ETH from ****************************************** to ******************************************
✅ API Call: POST /generate-environment (149ms)
🎨 Environment Creation Tracked: Desert oasis with Environment by creator_1
✅ Environment created by creator_1: Desert oasis with Environment
✅ Environment created: Desert oasis with Environment by creator_1
✅ Environment created: Desert oasis with Environment by creator_1
🎨 creator_2 creating environment: "Cyberpunk neon cityscape with rain"
💸 creator_2 spending 2500 ETH for: Reality Warp: Cyberpunk neon cityscape with rain
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Cyberpunk neon cityscape with rain
🔢 Next nonce for creator_2: 0
✅ REAL blockchain transaction mined: 0x9f9a8649bac0e5dd04fa311852491cb95bc22335c1a034034d2475db6928465c
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
💰 ETH Transfer Tracked: 2500 ETH from ****************************************** to ******************************************
✅ API Call: POST /generate-environment (131ms)
🎨 Environment Creation Tracked: Cyberpunk neon cityscape Environment by creator_2
✅ Environment created by creator_2: Cyberpunk neon cityscape Environment
✅ Environment created: Cyberpunk neon cityscape Environment by creator_2
✅ Environment created: Cyberpunk neon cityscape Environment by creator_2
💰 Phase 2: Mystical Environment Purchases
❌ Test scenario failed: ReferenceError: window is not defined
    at UrlConfig.getApiBaseUrl (file:///home/<USER>/Downloads/AIGames/OrangeDefense/src/utils/UrlConfig.js:21:46)
    at new EnvironmentTracker (file:///home/<USER>/Downloads/AIGames/OrangeDefense/src/managers/EnvironmentTracker.js:16:36)
    at TokenomicsStressTest.runCreatorRewardTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:881:36)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:731:17)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:189:26)
❌ Stress test failed: ReferenceError: window is not defined
    at UrlConfig.getApiBaseUrl (file:///home/<USER>/Downloads/AIGames/OrangeDefense/src/utils/UrlConfig.js:21:46)
    at new EnvironmentTracker (file:///home/<USER>/Downloads/AIGames/OrangeDefense/src/managers/EnvironmentTracker.js:16:36)
    at TokenomicsStressTest.runCreatorRewardTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:881:36)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:731:17)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:189:26)
🔍 Queue health monitoring stopped
🧹 Cleaning up rate limiter resources...
📊 Final rate limiter status: {}
✅ Rate limiter cleanup completed
❌ Stress test failed: ReferenceError: window is not defined
    at UrlConfig.getApiBaseUrl (file:///home/<USER>/Downloads/AIGames/OrangeDefense/src/utils/UrlConfig.js:21:46)
    at new EnvironmentTracker (file:///home/<USER>/Downloads/AIGames/OrangeDefense/src/managers/EnvironmentTracker.js:16:36)
    at TokenomicsStressTest.runCreatorRewardTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:881:36)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:731:17)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:189:26)
/**
 * Retry System for Stress Testing
 *
 * Provides retry mechanisms and external API mocking for stress testing.
 * IMPORTANT: This system should ONLY mock external APIs (Groq, Fal.ai) and provide
 * retry logic. It should NEVER mock game functions as the objective is to test
 * the actual game functions, not simulate them.
 */

export class RetrySystem {
    constructor(config = {}) {
        this.config = config;
        this.retryStrategies = new Map();
        this.retryHistory = [];
        this.maxHistorySize = 100;

        // Initialize default retry strategies
        this.initializeDefaultStrategies();
    }

    /**
     * Initialize default retry strategies for common operations
     * ONLY includes retry mechanisms and external API mocking
     */
    initializeDefaultStrategies() {
        // External API calls (Groq, Fal.ai) - these can be mocked to save credits
        this.registerRetryStrategy('external-api-groq', [
            { type: 'retry-with-exponential-backoff', baseDelay: 2000, maxRetries: 3 },
            { type: 'mock-external-api', mockResponse: { text: 'Mock Groq response for testing' } }
        ]);

        this.registerRetryStrategy('external-api-fal', [
            { type: 'retry-with-exponential-backoff', baseDelay: 2000, maxRetries: 3 },
            { type: 'mock-external-api', mockResponse: { image_url: 'https://mock-fal-image.com/test.jpg' } }
        ]);

        // Network operations - only retry, no mocking
        this.registerRetryStrategy('network-request', [
            { type: 'retry-with-delay', delay: 1000, maxRetries: 3 },
            { type: 'retry-with-exponential-backoff', baseDelay: 2000, maxRetries: 5 }
        ]);

        // Database operations - only retry, no mocking
        this.registerRetryStrategy('database-operation', [
            { type: 'retry-with-delay', delay: 500, maxRetries: 2 },
            { type: 'retry-with-exponential-backoff', baseDelay: 1000, maxRetries: 3 }
        ]);
    }

    /**
     * Register a retry strategy for an operation
     */
    registerRetryStrategy(operationType, strategies) {
        this.retryStrategies.set(operationType, strategies);
    }

    /**
     * Execute operation with retry support
     */
    async executeWithRetry(operationType, primaryOperation, context = {}) {
        const strategies = this.retryStrategies.get(operationType) || [];
        let lastError = null;

        // Try primary operation first
        try {
            const result = await primaryOperation();
            return { success: true, result, usedFallback: false };
        } catch (error) {
            lastError = error;
            console.warn(`⚠️ Primary operation failed for ${operationType}: ${error.message}`);
        }

        // Try retry strategies in order
        for (let i = 0; i < strategies.length; i++) {
            const strategy = strategies[i];

            try {
                console.log(`🔄 Attempting retry strategy ${i + 1}/${strategies.length} for ${operationType}: ${strategy.type}`);

                const result = await this.executeRetryStrategy(strategy, operationType, context, lastError);

                if (result.success) {
                    this.recordRetryUsage(operationType, strategy, true);
                    return { success: true, result: result.data, usedRetry: true, retryStrategy: strategy };
                }
            } catch (retryError) {
                console.warn(`⚠️ Retry strategy ${strategy.type} failed: ${retryError.message}`);
                this.recordRetryUsage(operationType, strategy, false, retryError);
                lastError = retryError;
            }
        }

        // All retries failed
        this.recordRetryUsage(operationType, null, false, lastError);
        throw new Error(`All retry strategies failed for ${operationType}. Last error: ${lastError.message}`);
    }

    /**
     * Execute a specific retry strategy
     * Only supports retry mechanisms and external API mocking
     */
    async executeRetryStrategy(strategy, operationType, context, originalError) {
        switch (strategy.type) {
            case 'retry-with-delay':
                return this.retryWithDelay(strategy, context.primaryOperation);

            case 'retry-with-exponential-backoff':
                return this.retryWithExponentialBackoff(strategy, context.primaryOperation);

            case 'mock-external-api':
                return this.mockExternalAPI(strategy, context);

            default:
                throw new Error(`Unknown retry strategy: ${strategy.type}. Only retry mechanisms and external API mocking are supported.`);
        }
    }

    /**
     * Retry with fixed delay
     */
    async retryWithDelay(strategy, operation) {
        const { delay = 5000, maxRetries = 3 } = strategy;
        
        for (let i = 0; i < maxRetries; i++) {
            if (i > 0) {
                await this.delay(delay);
            }
            
            try {
                const result = await operation();
                return { success: true, data: result };
            } catch (error) {
                if (i === maxRetries - 1) {
                    throw error;
                }
            }
        }
    }

    /**
     * Retry with exponential backoff
     */
    async retryWithExponentialBackoff(strategy, operation) {
        const { baseDelay = 2000, maxRetries = 5 } = strategy;
        
        for (let i = 0; i < maxRetries; i++) {
            if (i > 0) {
                const delay = baseDelay * Math.pow(2, i - 1) + Math.random() * 1000;
                await this.delay(delay);
            }
            
            try {
                const result = await operation();
                return { success: true, data: result };
            } catch (error) {
                if (i === maxRetries - 1) {
                    throw error;
                }
            }
        }
    }

    /**
     * Mock external API response (for Groq, Fal.ai, etc.)
     * This is the ONLY mocking allowed - for external APIs to save credits during testing
     */
    async mockExternalAPI(strategy, context) {
        const { mockResponse = {} } = strategy;

        console.warn(`⚠️ Using mock response for external API to save credits during testing`);

        return {
            success: true,
            data: {
                ...mockResponse,
                mock: true,
                timestamp: Date.now(),
                reason: 'external_api_mock_for_testing'
            }
        };
    }

    /**
     * Record retry usage for analytics
     */
    recordRetryUsage(operationType, strategy, success, error = null) {
        const record = {
            timestamp: Date.now(),
            operationType,
            strategy: strategy ? strategy.type : 'none',
            success,
            error: error ? error.message : null
        };

        this.retryHistory.push(record);

        // Maintain history size
        if (this.retryHistory.length > this.maxHistorySize) {
            this.retryHistory.shift();
        }
    }

    /**
     * Get retry statistics
     */
    getRetryStats() {
        const stats = {
            totalRetries: this.retryHistory.length,
            successfulRetries: this.retryHistory.filter(r => r.success).length,
            failedRetries: this.retryHistory.filter(r => !r.success).length,
            byOperationType: {},
            byStrategy: {},
            externalAPIMocks: this.retryHistory.filter(r => r.strategy === 'mock-external-api').length
        };

        // Group by operation type
        for (const record of this.retryHistory) {
            if (!stats.byOperationType[record.operationType]) {
                stats.byOperationType[record.operationType] = { total: 0, successful: 0 };
            }
            stats.byOperationType[record.operationType].total++;
            if (record.success) {
                stats.byOperationType[record.operationType].successful++;
            }
        }

        // Group by strategy
        for (const record of this.retryHistory) {
            if (!stats.byStrategy[record.strategy]) {
                stats.byStrategy[record.strategy] = { total: 0, successful: 0 };
            }
            stats.byStrategy[record.strategy].total++;
            if (record.success) {
                stats.byStrategy[record.strategy].successful++;
            }
        }

        return stats;
    }

    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

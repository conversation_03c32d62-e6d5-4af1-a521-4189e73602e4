/**
 * Transaction Tracking System
 * 
 * Monitors all blockchain transactions and API calls during stress testing.
 * Provides real-time tracking of token flows, ETH transfers, and creator rewards.
 */

import { ethers } from 'ethers';
import fetch from 'node-fetch';

export class TransactionTracker {
    constructor(config) {
        this.config = config;
        this.isTracking = false;
        
        // Transaction storage
        this.transactions = [];
        this.apiCalls = [];
        this.blockchainEvents = [];
        
        // Real-time metrics
        this.metrics = {
            totalTransactions: 0,
            totalTokensAwarded: 0,
            totalTokensSpent: 0,
            totalETHTransferred: 0,
            totalCreatorRewards: 0,
            errorCount: 0,
            averageResponseTime: 0
        };

        // Event listeners
        this.eventListeners = [];
        
        console.log('📊 TransactionTracker initialized');
    }

    /**
     * Start tracking transactions
     */
    async start() {
        if (this.isTracking) {
            console.warn('⚠️ TransactionTracker already running');
            return;
        }

        this.isTracking = true;
        this.startTime = Date.now();
        
        console.log('🚀 TransactionTracker started');
    }

    /**
     * Stop tracking transactions
     */
    async stop() {
        this.isTracking = false;
        this.endTime = Date.now();
        
        console.log('🛑 TransactionTracker stopped');
        console.log(`📊 Tracked ${this.transactions.length} transactions in ${this.endTime - this.startTime}ms`);
    }

    /**
     * Track API call
     */
    trackAPICall(endpoint, method, requestData, responseData, duration, error = null) {
        if (!this.isTracking) return;

        const apiCall = {
            id: `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: Date.now(),
            endpoint,
            method,
            requestData: this.sanitizeData(requestData),
            responseData: this.sanitizeData(responseData),
            duration,
            error: error ? error.message : null,
            success: !error
        };

        this.apiCalls.push(apiCall);
        this.updateMetrics(apiCall);

        if (error) {
            console.error(`❌ API Call Failed: ${method} ${endpoint} - ${error.message}`);
        } else {
            console.log(`✅ API Call: ${method} ${endpoint} (${duration}ms)`);
        }

        return apiCall.id;
    }

    /**
     * Track token award transaction
     */
    trackTokenAward(userId, amount, reason, transactionId, metadata = {}) {
        console.log(`🔍 TransactionTracker.trackTokenAward called: isTracking=${this.isTracking}, userId=${userId}, amount=${amount}`);

        if (!this.isTracking) {
            console.log(`⚠️ TransactionTracker not tracking, skipping award for ${userId}`);
            return;
        }

        const transaction = {
            id: transactionId,
            type: 'token_award',
            timestamp: Date.now(),
            userId,
            amount,
            reason,
            metadata,
            direction: 'inbound' // Tokens coming to user
        };

        this.transactions.push(transaction);
        this.metrics.totalTokensAwarded += amount;
        this.metrics.totalTransactions++;

        console.log(`🎁 Token Award Tracked: ${amount} tokens to ${userId}`);
        return transaction;
    }

    /**
     * Track token spend transaction
     */
    trackTokenSpend(userId, amount, reason, transactionId, metadata = {}) {
        if (!this.isTracking) return;

        const transaction = {
            id: transactionId,
            type: 'token_spend',
            timestamp: Date.now(),
            userId,
            amount,
            reason,
            metadata,
            direction: 'outbound' // Tokens leaving user
        };

        this.transactions.push(transaction);
        this.metrics.totalTokensSpent += amount;
        this.metrics.totalTransactions++;

        console.log(`💸 Token Spend Tracked: ${amount} tokens from ${userId}`);
        return transaction;
    }

    /**
     * Track ETH transfer (creator rewards)
     */
    trackETHTransfer(fromAddress, toAddress, amount, reason, txHash, metadata = {}) {
        if (!this.isTracking) return;

        const transaction = {
            id: txHash,
            type: 'eth_transfer',
            timestamp: Date.now(),
            fromAddress,
            toAddress,
            amount: parseFloat(amount),
            reason,
            txHash,
            metadata,
            direction: 'transfer'
        };

        this.transactions.push(transaction);
        this.metrics.totalETHTransferred += parseFloat(amount);
        this.metrics.totalTransactions++;

        // Track creator rewards specifically
        if (reason && reason.includes('creator')) {
            this.metrics.totalCreatorRewards += parseFloat(amount);
        }

        console.log(`💰 ETH Transfer Tracked: ${amount} ETH from ${fromAddress} to ${toAddress}`);
        return transaction;
    }

    /**
     * Track creator reward distribution
     */
    trackCreatorReward(creatorId, amount, reason, transactionId, metadata = {}) {
        if (!this.isTracking) return;

        const transaction = {
            id: transactionId,
            type: 'creator_reward',
            timestamp: Date.now(),
            creatorId,
            amount: parseFloat(amount),
            reason,
            metadata,
            direction: 'inbound' // Reward coming to creator
        };

        this.transactions.push(transaction);
        this.metrics.totalCreatorRewards += parseFloat(amount);
        this.metrics.totalTransactions++;

        console.log(`🎨 Creator Reward Tracked: ${amount} tokens to ${creatorId} for ${reason}`);
        return transaction;
    }


    /**
     * Track environment creation
     */
    trackEnvironmentCreation(creatorId, environmentData, cost, creatorReward) {
        if (!this.isTracking) return;

        const transaction = {
            id: `env_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            type: 'environment_creation',
            timestamp: Date.now(),
            creatorId,
            environmentName: environmentData.name,
            cost,
            creatorReward,
            metadata: {
                environmentType: environmentData.gameplayModifiers?.environmentType,
                imageGenerated: !!environmentData.imageData
            }
        };

        this.transactions.push(transaction);
        this.metrics.totalTransactions++;

        console.log(`🎨 Environment Creation Tracked: ${environmentData.name} by ${creatorId}`);
        return transaction;
    }

    /**
     * Track error occurrence
     */
    trackError(type, message, context = {}) {
        if (!this.isTracking) return;

        const error = {
            id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: Date.now(),
            type,
            message,
            context
        };

        this.blockchainEvents.push(error);
        this.metrics.errorCount++;

        console.error(`❌ Error Tracked: ${type} - ${message}`);
        return error;
    }

    /**
     * Update real-time metrics
     */
    updateMetrics(apiCall) {
        // Update average response time
        const totalDuration = this.apiCalls.reduce((sum, call) => sum + call.duration, 0);
        this.metrics.averageResponseTime = totalDuration / this.apiCalls.length;

        // Update error count
        if (apiCall.error) {
            this.metrics.errorCount++;
        }
    }

    /**
     * Get real-time metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            trackingDuration: this.isTracking ? Date.now() - this.startTime : this.endTime - this.startTime,
            transactionsPerSecond: this.calculateTransactionsPerSecond(),
            errorRate: this.calculateErrorRate()
        };
    }

    /**
     * Calculate transactions per second
     */
    calculateTransactionsPerSecond() {
        const duration = this.isTracking ? Date.now() - this.startTime : this.endTime - this.startTime;
        return duration > 0 ? (this.transactions.length / (duration / 1000)).toFixed(2) : 0;
    }

    /**
     * Calculate error rate
     */
    calculateErrorRate() {
        const totalOperations = this.apiCalls.length + this.transactions.length;
        return totalOperations > 0 ? ((this.metrics.errorCount / totalOperations) * 100).toFixed(2) : 0;
    }

    /**
     * Get transaction summary by type
     */
    getTransactionSummary() {
        const summary = {
            token_award: { count: 0, totalAmount: 0 },
            token_spend: { count: 0, totalAmount: 0 },
            eth_transfer: { count: 0, totalAmount: 0 },
            creator_reward: { count: 0, totalAmount: 0 },
            environment_creation: { count: 0, totalRewards: 0 }
        };

        this.transactions.forEach(tx => {
            if (summary[tx.type]) {
                summary[tx.type].count++;
                if (tx.amount) {
                    summary[tx.type].totalAmount += tx.amount;
                }
                if (tx.creatorReward) {
                    summary[tx.type].totalRewards += tx.creatorReward;
                }
            }
        });

        return summary;
    }

    /**
     * Get user activity summary
     */
    getUserActivitySummary() {
        const userActivity = {};

        this.transactions.forEach(tx => {
            const userId = tx.userId || tx.creatorId;
            if (userId) {
                if (!userActivity[userId]) {
                    userActivity[userId] = {
                        totalTransactions: 0,
                        tokensAwarded: 0,
                        tokensSpent: 0,
                        creatorRewards: 0,
                        environmentsCreated: 0
                    };
                }

                userActivity[userId].totalTransactions++;

                if (tx.type === 'token_award') {
                    userActivity[userId].tokensAwarded += tx.amount;
                } else if (tx.type === 'token_spend') {
                    userActivity[userId].tokensSpent += tx.amount;
                } else if (tx.type === 'creator_reward') {
                    userActivity[userId].creatorRewards += tx.amount;
                } else if (tx.type === 'environment_creation') {
                    userActivity[userId].environmentsCreated++;
                }
            }
        });

        return userActivity;
    }

    /**
     * Get creator reward analysis
     */
    getCreatorRewardAnalysis() {
        const creatorTransactions = this.transactions.filter(tx =>
            (tx.type === 'eth_transfer' && tx.reason && tx.reason.includes('creator')) ||
            (tx.type === 'creator_reward') ||
            (tx.type === 'token_award' && tx.reason && tx.reason.includes('creator'))
        );

        const environmentPurchases = this.transactions.filter(tx =>
            tx.type === 'token_spend' && tx.reason && tx.reason.includes('Environment')
        );

        return {
            totalCreatorRewards: this.metrics.totalCreatorRewards,
            creatorRewardTransactions: creatorTransactions.length,
            environmentPurchases: environmentPurchases.length,
            averageRewardPerEnvironment: environmentPurchases.length > 0
                ? (this.metrics.totalCreatorRewards / environmentPurchases.length).toFixed(4)
                : 0,
            rewardDistributionAccuracy: this.calculateRewardAccuracy(environmentPurchases, creatorTransactions)
        };
    }

    /**
     * Calculate reward distribution accuracy (should be 50% of environment purchases)
     */
    calculateRewardAccuracy(purchases, rewards) {
        if (purchases.length === 0) return 100;

        const totalPurchaseAmount = purchases.reduce((sum, p) => sum + p.amount, 0);
        const expectedRewards = totalPurchaseAmount * 0.5; // 50% should go to creators
        const actualRewards = this.metrics.totalCreatorRewards;

        const accuracy = expectedRewards > 0 ? ((actualRewards / expectedRewards) * 100).toFixed(2) : 100;
        return Math.min(100, accuracy); // Cap at 100%
    }

    /**
     * Export transaction data for analysis
     */
    exportData() {
        return {
            transactions: this.transactions,
            apiCalls: this.apiCalls,
            blockchainEvents: this.blockchainEvents,
            metrics: this.getMetrics(),
            summary: this.getTransactionSummary(),
            userActivity: this.getUserActivitySummary(),
            creatorRewardAnalysis: this.getCreatorRewardAnalysis(),
            exportTimestamp: Date.now()
        };
    }

    /**
     * Sanitize sensitive data before logging
     */
    sanitizeData(data) {
        if (!data) return data;
        
        const sanitized = JSON.parse(JSON.stringify(data));
        
        // Remove sensitive fields
        if (sanitized.privateKey) delete sanitized.privateKey;
        if (sanitized.password) delete sanitized.password;
        if (sanitized.apiKey) delete sanitized.apiKey;
        
        return sanitized;
    }

    /**
     * Get filtered transactions by criteria
     */
    getTransactions(filter = {}) {
        let filtered = [...this.transactions];

        if (filter.type) {
            filtered = filtered.filter(tx => tx.type === filter.type);
        }

        if (filter.userId) {
            filtered = filtered.filter(tx => tx.userId === filter.userId);
        }

        if (filter.startTime) {
            filtered = filtered.filter(tx => tx.timestamp >= filter.startTime);
        }

        if (filter.endTime) {
            filtered = filtered.filter(tx => tx.timestamp <= filter.endTime);
        }

        return filtered;
    }

    /**
     * Clear all tracking data
     */
    clear() {
        this.transactions = [];
        this.apiCalls = [];
        this.blockchainEvents = [];
        this.metrics = {
            totalTransactions: 0,
            totalTokensAwarded: 0,
            totalTokensSpent: 0,
            totalETHTransferred: 0,
            totalCreatorRewards: 0,
            errorCount: 0,
            averageResponseTime: 0
        };

        console.log('🧹 TransactionTracker data cleared');
    }
}
